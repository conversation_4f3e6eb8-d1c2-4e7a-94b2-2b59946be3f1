// Import secure storage utilities
import { secureGet<PERSON>piKey, secureSetApiKey } from '../utils/secureStorage.js';

document.addEventListener('DOMContentLoaded', async function () {
  // Get elements
  const apiKeyInput = document.getElementById('apiKey');
  const customPromptInput = document.getElementById('customPrompt');
  const fontFamilySelect = document.getElementById('fontFamily');
  const fontSizeSelect = document.getElementById('fontSize');
  // Translation Elements
  const translateInput = document.getElementById('translate-input');
  const translateOutputDiv = document.getElementById('translate-output');
  const viInput = document.getElementById('vi-input');
  const enOutputDiv = document.getElementById('en-output');
  const customPromptViEnInput = document.getElementById('customPromptViEn');
  const copyEnOutputButton = document.querySelector('.vi-en-section .output-container .copy-button');
  // --- End Element Retrieval ---
  const autoPosition = document.getElementById('autoPosition');
  const defaultPosition = document.getElementById('defaultPosition');

  // Initialize cache stats display
  const cacheStatsElement = document.createElement('div');
  cacheStatsElement.id = 'cache-stats';
  cacheStatsElement.className = 'cache-stats';
  document.querySelector('.info').appendChild(cacheStatsElement);

  // Load cache stats
  updateCacheStats();

  // Load saved settings
  try {
    // Securely load API key
    const apiKey = await secureGetApiKey();
    if (apiKey) {
      apiKeyInput.value = apiKey;
    }

    // Load other settings
    chrome.storage.local.get(['customPrompt', 'customPromptViEn', 'fontFamily', 'fontSize'], function (result) {

      if (result.customPrompt) {
        customPromptInput.value = result.customPrompt;
        // Auto-resize the textarea after content is loaded
        setTimeout(() => autoResizeTextarea(customPromptInput), 10);
      }

      // Load saved Vi-En custom prompt
      if (result.customPromptViEn) {
        customPromptViEnInput.value = result.customPromptViEn;
        // Auto-resize the textarea after content is loaded
        setTimeout(() => autoResizeTextarea(customPromptViEnInput), 10);
      }

      if (result.fontFamily) {
        fontFamilySelect.value = result.fontFamily;
      }

      if (result.fontSize) {
        fontSizeSelect.value = result.fontSize;
      }
    });
  } catch (error) {
    console.error('Error loading settings:', error);
    // Show error message to user only if it's a serious error
    if (error.message && !error.message.includes('obfuscated')) {
      const errorDiv = document.createElement('div');
      errorDiv.className = 'error-message';
      errorDiv.textContent = 'Failed to load settings. Please re-enter your API key.';
      document.querySelector('.api-key-container').after(errorDiv);
    }
  }

  // --- Helper Functions ---

  // === Auto-resize textarea function ===
  function autoResizeTextarea(textarea) {
    // Save current cursor position
    const selectionStart = textarea.selectionStart;
    const selectionEnd = textarea.selectionEnd;

    // Reset height to auto to calculate scrollHeight correctly
    textarea.classList.add('height-auto');
    textarea.classList.remove('custom-height');

    // Set minimum height to ensure correct calculation
    const minHeight = parseInt(window.getComputedStyle(textarea).lineHeight, 10) * 1.5 || 40;

    // Calculate new height based on content
    const newHeight = Math.max(minHeight, textarea.scrollHeight);

    // Apply new height using CSS custom property
    textarea.style.setProperty('--custom-height', newHeight + 'px');
    textarea.classList.remove('height-auto');
    textarea.classList.add('custom-height');

    // Restore cursor position
    textarea.setSelectionRange(selectionStart, selectionEnd);
  }

  // Function to update and display cache stats
  async function updateCacheStats() {
    try {
      const statsElement = document.getElementById('cache-stats');
      if (!statsElement) return;

      chrome.storage.local.get(['cacheStats', 'translationCache', 'cacheVersion'], function (result) {
        const stats = result.cacheStats || { hits: 0, misses: 0 };
        const cache = result.translationCache || {};
        const cacheSize = Object.keys(cache).length;
        const cacheVersion = result.cacheVersion || 'unknown';

        // Calculate hit rate
        const totalRequests = stats.hits + stats.misses;
        const hitRate = totalRequests > 0 ? Math.round((stats.hits / totalRequests) * 100) : 0;

        // Format last cleanup time
        let lastCleanup = 'Never';
        if (stats.lastCleanup) {
          const date = new Date(stats.lastCleanup);
          lastCleanup = date.toLocaleString();
        }

        // Calculate storage metrics
        const bytesStored = stats.bytesStored || 0;
        const compressionSavings = stats.compressionSavings || 0;
        const kbStored = (bytesStored / 1024).toFixed(2);
        const kbSaved = (compressionSavings / 1024).toFixed(2);

        // Count entries by language direction
        let viEnCount = 0;
        let enViCount = 0;

        Object.keys(cache).forEach(key => {
          if (key.startsWith('vi-en')) {
            viEnCount++;
          } else if (key.startsWith('en-vi')) {
            enViCount++;
          }
        });

        // Update stats display
        statsElement.innerHTML = `
          <details>
            <summary>Cache Statistics</summary>
            <div class="stats-content">
              <p>Cache Size: ${cacheSize} entries (${enViCount} En→Vi, ${viEnCount} Vi→En)</p>
              <p>Cache Hits: ${stats.hits}</p>
              <p>Cache Misses: ${stats.misses}</p>
              <p>Hit Rate: ${hitRate}%</p>
              <p>Storage: ${kbStored} KB (saved ${kbSaved} KB)</p>
              <p>Last Cleanup: ${lastCleanup}</p>
              <p>Version: ${cacheVersion}</p>
              <div class="stats-actions">
                <button id="clear-cache">Clear Cache</button>
                <button id="run-cleanup">Run Cleanup</button>
              </div>
            </div>
          </details>
        `;

        // Add button listeners
        document.getElementById('clear-cache')?.addEventListener('click', clearCache);
        document.getElementById('run-cleanup')?.addEventListener('click', runManualCleanup);
      });
    } catch (error) {
      console.error('Error updating cache stats:', error);
    }
  }

  // Function to clear the translation cache
  async function clearCache() {
    try {
      if (confirm('Are you sure you want to clear the entire translation cache?')) {
        await chrome.storage.local.set({
          translationCache: {},
          cacheStats: {
            hits: 0,
            misses: 0,
            lastCleanup: Date.now(),
            bytesStored: 0,
            compressionSavings: 0
          }
        });
        updateCacheStats();
        showToast('Cache cleared successfully!');
      }
    } catch (error) {
      console.error('Error clearing cache:', error);
      showToast('Failed to clear cache: ' + error.message, 'error');
    }
  }

  // Function to run a manual cache cleanup
  async function runManualCleanup() {
    try {
      showToast('Running cache cleanup...', 'info');

      // Send message to background script to run cleanup
      chrome.runtime.sendMessage({ action: 'runCacheCleanup' }, function (response) {
        if (chrome.runtime.lastError) {
          showToast('Error: ' + chrome.runtime.lastError.message, 'error');
          return;
        }

        if (response && response.success) {
          showToast('Cache cleanup completed successfully!');
          updateCacheStats(); // Refresh stats
        } else {
          showToast('Cache cleanup failed', 'error');
        }
      });
    } catch (error) {
      console.error('Error running cache cleanup:', error);
      showToast('Failed to run cleanup: ' + error.message, 'error');
    }
  }

  // Helper function to show toast notifications
  function showToast(message, type = 'success') {
    const toast = document.createElement('div');
    toast.className = `toast toast-${type}`;
    toast.textContent = message;

    document.body.appendChild(toast);

    // Trigger animation
    setTimeout(() => toast.classList.add('show'), 10);

    // Auto remove after 3 seconds
    setTimeout(() => {
      toast.classList.remove('show');
      setTimeout(() => toast.remove(), 300); // Wait for fade out animation
    }, 3000);
  }

  // Debounced auto-save for input fields
  function setupAutoSave(inputElement, storageKey, feedback = true) {
    let saveTimeout;
    inputElement.addEventListener('input', function () {
      clearTimeout(saveTimeout);
      saveTimeout = setTimeout(async () => {
        const value = inputElement.value.trim();

        try {
          // Handle API key securely
          if (storageKey === 'apiKey') {
            await secureSetApiKey(value);
          } else {
            // Regular storage for non-sensitive data
            await chrome.storage.local.set({ [storageKey]: value });
          }

          // Visual feedback using CSS classes instead of inline styles
          if (feedback) {
            inputElement.classList.add('bg-saved', 'flash-animation');
            inputElement.classList.remove('bg-original');

            // Show permanent storage indicator for API key
            if (storageKey === 'apiKey' && value) {
              showToast('API key saved permanently', 'success');
            }

            setTimeout(() => {
              inputElement.classList.remove('bg-saved', 'flash-animation');
              inputElement.classList.add('bg-original');
            }, 300);
          }
        } catch (error) {
          console.error(`Error saving ${storageKey}:`, error);
          alert(`Failed to save ${storageKey}: ${error.message}`);
        }
      }, 500); // 500ms debounce
    });
  }

  // Save font setting and notify content scripts
  function setupFontSettingListener(selectElement, storageKey, messageAction) {
    selectElement.addEventListener('change', function () {
      const value = this.value;
      // Save the setting to storage
      chrome.storage.local.set({ [storageKey]: value });

      // Notify all active tabs to update font style immediately
      chrome.tabs.query({}, function (tabs) {
        tabs.forEach(function (tab) {
          // Only send messages to tabs where our content script is running
          // This prevents the "Receiving end does not exist" error
          try {
            chrome.tabs.sendMessage(tab.id, {
              action: messageAction,
              value: value
            }, function (response) {
              // Check for error in the callback
              if (chrome.runtime.lastError) {
                // Silently ignore the error - this means the content script isn't loaded in this tab
                console.log(`Message not sent to tab ${tab.id}: ${chrome.runtime.lastError.message}`);
              }
            });
          } catch (error) {
            console.log(`Error sending message to tab ${tab.id}: ${error.message}`);
          }
        });
      });

      // Also update the preview in popup
      const previewElements = [
        document.getElementById('translate-output'),
        document.getElementById('en-output')
      ];

      previewElements.forEach(element => {
        if (element && element.innerHTML.trim()) {
          if (storageKey === 'fontFamily') {
            // Remove all font family classes
            element.classList.forEach(className => {
              if (className.startsWith('font-') && !className.startsWith('font-size-')) {
                element.classList.remove(className);
              }
            });

            // Add the appropriate font family class
            let fontClass = '';
            if (value === '-apple-system, BlinkMacSystemFont, system-ui, sans-serif') {
              fontClass = 'font-system';
            } else if (value === "'Baloo 2', cursive") {
              fontClass = 'font-baloo';
            } else if (value === "'Be Vietnam Pro', sans-serif") {
              fontClass = 'font-be-vietnam';
            } else if (value === "'Cabin', sans-serif") {
              fontClass = 'font-cabin';
            } else if (value === "'Comfortaa', cursive") {
              fontClass = 'font-comfortaa';
            } else if (value === "'Crimson Pro', serif") {
              fontClass = 'font-crimson-pro';
            } else if (value === "'Crimson Text', serif") {
              fontClass = 'font-crimson-text';
            } else if (value === "'Fira Sans', sans-serif") {
              fontClass = 'font-fira';
            } else if (value === "'Fraunces', serif") {
              fontClass = 'font-fraunces';
            } else if (value === "'Geologica', sans-serif") {
              fontClass = 'font-geologica';
            } else if (value === "'Lexend Deca', sans-serif") {
              fontClass = 'font-lexend';
            } else if (value === "'Literata', serif") {
              fontClass = 'font-literata';
            } else if (value === "'Mali', cursive") {
              fontClass = 'font-mali';
            } else if (value === "'Manrope', sans-serif") {
              fontClass = 'font-manrope';
            } else if (value === "'Maven Pro', sans-serif") {
              fontClass = 'font-maven';
            } else if (value === "'Montserrat', sans-serif") {
              fontClass = 'font-montserrat';
            } else if (value === "'Mulish', sans-serif") {
              fontClass = 'font-mulish';
            } else if (value === "'Newsreader', serif") {
              fontClass = 'font-newsreader';
            } else if (value === "'Noto Sans', sans-serif") {
              fontClass = 'font-noto';
            } else if (value === "'Nunito Sans', sans-serif") {
              fontClass = 'font-nunito-sans';
            } else if (value === "'Nunito', sans-serif") {
              fontClass = 'font-nunito';
            } else if (value === "'Open Sans', sans-serif") {
              fontClass = 'font-open-sans';
            } else if (value === "'Petrona', serif") {
              fontClass = 'font-petrona';
            } else if (value === "'Questrial', sans-serif") {
              fontClass = 'font-questrial';
            } else if (value === "'Quicksand', sans-serif") {
              fontClass = 'font-quicksand';
            } else if (value === "'Raleway', sans-serif") {
              fontClass = 'font-raleway';
            } else if (value === "'Roboto Mono', monospace") {
              fontClass = 'font-roboto-mono';
            } else if (value === "'Roboto', sans-serif") {
              fontClass = 'font-roboto';
            } else if (value === "'Space Grotesk', sans-serif") {
              fontClass = 'font-space-grotesk';
            } else if (value === "'Varela Round', sans-serif") {
              fontClass = 'font-varela';
            } else if (value === "'Work Sans', sans-serif") {
              fontClass = 'font-work-sans';
            }

            if (fontClass) {
              element.classList.add(fontClass);
            }
          } else if (storageKey === 'fontSize') {
            // Remove all font size classes
            element.classList.forEach(className => {
              if (className.startsWith('font-size-')) {
                element.classList.remove(className);
              }
            });

            // Add the appropriate font size class
            const fontSize = parseInt(value, 10);
            if (fontSize >= 8 && fontSize <= 32) {
              element.classList.add(`font-size-${fontSize}`);
            }
          }
        }
      });
    });
  }

  // Get position select group element
  const positionSelectGroup = document.querySelector('.position-select-group');

  // Function to update position select group state
  function updatePositionSelectState(isAutoEnabled) {
    defaultPosition.disabled = isAutoEnabled;
    if (isAutoEnabled) {
      positionSelectGroup.classList.remove('active');
    } else {
      positionSelectGroup.classList.add('active');
    }
  }

  // Restore settings
  chrome.storage.local.get(['autoPosition', 'defaultPosition'], function (result) {
    if (typeof result.autoPosition === 'boolean') {
      autoPosition.checked = result.autoPosition;
    }
    if (typeof result.defaultPosition === 'string') {
      defaultPosition.value = result.defaultPosition;
    }
    // Update position select group state
    updatePositionSelectState(autoPosition.checked);
  });

  // Save on change
  autoPosition.addEventListener('change', function () {
    chrome.storage.local.set({ autoPosition: autoPosition.checked });
    updatePositionSelectState(autoPosition.checked);
  });
  defaultPosition.addEventListener('change', function () {
    chrome.storage.local.set({ defaultPosition: defaultPosition.value });
  });

  // Core translation functions have been replaced with translateInputWithHistory and translateViEnWithHistory

  // --- Event Listeners Setup ---

  // Setup auto-saving for inputs
  setupAutoSave(apiKeyInput, 'apiKey', true);
  setupAutoSave(customPromptInput, 'customPrompt');
  setupAutoSave(customPromptViEnInput, 'customPromptViEn');

  // Setup font setting listeners
  setupFontSettingListener(fontFamilySelect, 'fontFamily', 'updateFontStyle');
  setupFontSettingListener(fontSizeSelect, 'fontSize', 'updateFontSize'); // Corrected action name

  // Add keyboard navigation for font select
  fontFamilySelect.addEventListener('keydown', function (event) {
    if (event.key === 'ArrowUp' || event.key === 'ArrowDown') {
      // Prevent default to stop dropdown from opening
      event.preventDefault();

      const options = this.options;
      const currentIndex = this.selectedIndex;
      let newIndex;

      if (event.key === 'ArrowUp') {
        // Move up in the list (decrease index)
        newIndex = currentIndex > 0 ? currentIndex - 1 : options.length - 1;
      } else {
        // Move down in the list (increase index)
        newIndex = currentIndex < options.length - 1 ? currentIndex + 1 : 0;
      }

      // Set the new selected option
      this.selectedIndex = newIndex;

      // Trigger the change event to apply the font immediately
      this.dispatchEvent(new Event('change'));
    }
  });

  // Add similar keyboard navigation for font size select
  fontSizeSelect.addEventListener('keydown', function (event) {
    if (event.key === 'ArrowUp' || event.key === 'ArrowDown') {
      // Prevent default to stop dropdown from opening
      event.preventDefault();

      const currentValue = parseInt(this.value, 10);
      let newValue;

      if (event.key === 'ArrowUp') {
        // Increase font size
        newValue = Math.min(currentValue + 1, parseInt(this.max, 10));
      } else {
        // Decrease font size
        newValue = Math.max(currentValue - 1, parseInt(this.min, 10));
      }

      // Set the new value
      this.value = newValue;

      // Trigger the change event to apply the font size immediately
      this.dispatchEvent(new Event('change'));
    }
  });

  // Add event listeners for the custom font size buttons
  const decreaseBtn = document.querySelector('.font-size-btn.decrease');
  const increaseBtn = document.querySelector('.font-size-btn.increase');

  decreaseBtn.addEventListener('click', function () {
    const currentValue = parseInt(fontSizeSelect.value, 10);
    const newValue = Math.max(currentValue - 1, parseInt(fontSizeSelect.min, 10));

    if (newValue !== currentValue) {
      fontSizeSelect.value = newValue;
      fontSizeSelect.dispatchEvent(new Event('change'));
    }
  });

  increaseBtn.addEventListener('click', function () {
    const currentValue = parseInt(fontSizeSelect.value, 10);
    const newValue = Math.min(currentValue + 1, parseInt(fontSizeSelect.max, 10));

    if (newValue !== currentValue) {
      fontSizeSelect.value = newValue;
      fontSizeSelect.dispatchEvent(new Event('change'));
    }
  });
  // General En-Vi Translation Listener
  translateInput.addEventListener('keydown', function (event) {
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault();
      const textToTranslate = translateInput.value.trim();
      // Retrieve the custom prompt (or default) and call the helper
      chrome.storage.local.get(['customPrompt'], function (result) {
        const prompt = result.customPrompt || 'Dịch thuật nội dung sau phù hợp với ngữ nghĩa người Việt Nam có thể hiểu rõ được hết ý của người viết là người Mỹ bản ngữ'; // Use same default as background
        translateInputWithHistory(textToTranslate, prompt);
      });
    }
  });
  // Vi-En Translation Listener
  viInput.addEventListener('keydown', function (event) {
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault();
      const textToTranslate = viInput.value.trim();
      // Construct the prompt and call the helper
      const customViEnPromptText = customPromptViEnInput.value.trim();
      let prompt = `Translate the following Vietnamese text to English:`;
      if (customViEnPromptText) {
        prompt += `\n\nInstruction: ${customViEnPromptText}`; // Add instruction prefix if custom prompt exists
      }
      translateViEnWithHistory(textToTranslate, prompt);
    }
  });

  // === Auto-resize all input textareas ===
  // List of all textareas to auto-resize
  const inputTextareas = [
    document.getElementById('translate-input'),
    document.getElementById('vi-input'),
    document.getElementById('customPrompt'),
    document.getElementById('customPromptViEn')
  ].filter(Boolean);

  inputTextareas.forEach(textarea => {
    // Initial resize after a small delay to ensure content is properly rendered
    setTimeout(() => autoResizeTextarea(textarea), 100);

    // On input (for editable textareas)
    textarea.addEventListener('input', function () {
      autoResizeTextarea(this);
    });

    // Also handle paste events which might add significant content
    textarea.addEventListener('paste', function () {
      // Use setTimeout to allow the paste to complete before resizing
      setTimeout(() => autoResizeTextarea(this), 10);
    });

    // Handle when the textarea gets focus
    textarea.addEventListener('focus', function () {
      autoResizeTextarea(this);
    });

    // Handle window resize events which might affect layout
    window.addEventListener('resize', function () {
      autoResizeTextarea(textarea);
    });

    // Handle when content is cleared or deleted
    textarea.addEventListener('keydown', function (e) {
      // Check for delete, backspace or clear operations
      if (e.key === 'Delete' || e.key === 'Backspace' ||
        (e.key === 'A' && e.ctrlKey) || (e.key === 'a' && e.ctrlKey)) {
        setTimeout(() => autoResizeTextarea(this), 0);
      }
    });
  });

  // Remove safeResizeOutput as divs resize automatically with CSS height:auto
  // Copy Button Logic for Vi-En Output
  copyEnOutputButton.addEventListener('click', function () {
    const textToCopy = enOutputDiv.textContent; // Copy text content of the div
    if (textToCopy) {
      navigator.clipboard.writeText(textToCopy).then(() => {
        // Optional: Visual feedback
        const originalContent = copyEnOutputButton.innerHTML; // Store original SVG/content
        copyEnOutputButton.textContent = 'Copied!';
        setTimeout(() => {
          copyEnOutputButton.innerHTML = originalContent; // Restore original SVG/content
        }, 1500); // Revert after 1.5 seconds
      }).catch(err => {
        console.error('Failed to copy text: ', err);
        // Optional: Provide error feedback to the user
        const originalContent = copyEnOutputButton.innerHTML; // Store original SVG/content
        copyEnOutputButton.textContent = 'Error!';
        setTimeout(() => {
          copyEnOutputButton.innerHTML = originalContent; // Restore original SVG/content
        }, 1500);
      });
    }
  });
  // --- Keyboard Shortcuts ---
  // Function to handle collapsible sections
  function setupCollapsibleSections() {
    // Get all collapsible sections
    const collapsibleSections = document.querySelectorAll('.primary-settings-collapse');
    const viEnSection = collapsibleSections[0];
    const enViSection = collapsibleSections[1];
    const settingsSection = collapsibleSections[2];

    // Function to close all sections except the one being opened
    function closeOtherSections(openSection) {
      collapsibleSections.forEach(section => {
        if (section !== openSection) {
          section.open = false;
        }
      });
    }

    // Add event listeners to each section
    collapsibleSections.forEach(section => {
      section.addEventListener('toggle', function (event) {
        if (this.open) {
          closeOtherSections(this);
        }
      });
    });

    return { viEnSection, enViSection, settingsSection, closeOtherSections };
  }

  function setupKeyboardShortcuts() {
    // Setup collapsible sections and get references
    const { viEnSection, enViSection, settingsSection, closeOtherSections } = setupCollapsibleSections();

    // Detect if we're on macOS
    const isMac = navigator.platform.toUpperCase().indexOf('MAC') >= 0;

    // Update keyboard shortcut displays for macOS users
    if (isMac) {
      // Get the shortcut elements
      const viEnShortcut = document.getElementById('vi-en-shortcut');
      const enViShortcut = document.getElementById('en-vi-shortcut');
      const settingsShortcut = document.getElementById('settings-shortcut');

      // Update the shortcut text to show Command instead of Alt
      if (viEnShortcut) {
        viEnShortcut.innerHTML = '<kbd>⌘</kbd><span>+</span><kbd>1</kbd>';
      }

      if (enViShortcut) {
        enViShortcut.innerHTML = '<kbd>⌘</kbd><span>+</span><kbd>2</kbd>';
      }

      if (settingsShortcut) {
        settingsShortcut.innerHTML = '<kbd>⌘</kbd><span>+</span><kbd>S</kbd>';
      }
    }

    // Log platform info for debugging
    console.log('Platform:', navigator.platform, 'isMac:', isMac);

    // Add keyboard event listener to the document
    document.addEventListener('keydown', function (event) {
      // Log key events for debugging
      console.log('Key event:', event.key,
        'Alt:', event.altKey,
        'Ctrl:', event.ctrlKey,
        'Meta:', event.metaKey);

      // For macOS, use Command (metaKey) instead of Alt
      // For Windows/Linux, use Alt key
      const modifierKey = isMac ? event.metaKey : event.altKey;

      // Command/Alt+1: Toggle Vietnamese to English section
      if (modifierKey && event.key === '1') {
        event.preventDefault();
        viEnSection.open = !viEnSection.open;
        if (viEnSection.open) {
          closeOtherSections(viEnSection);
          document.getElementById('vi-input').focus();
        }
      }

      // Command/Alt+2: Toggle English to Vietnamese section
      else if (modifierKey && event.key === '2') {
        event.preventDefault();
        enViSection.open = !enViSection.open;
        if (enViSection.open) {
          closeOtherSections(enViSection);
          document.getElementById('translate-input').focus();
        }
      }

      // Command/Alt+S: Toggle Settings section
      else if (modifierKey && (event.key === 's' || event.key === 'S')) {
        event.preventDefault();
        settingsSection.open = !settingsSection.open;
        if (settingsSection.open) {
          closeOtherSections(settingsSection);
          document.getElementById('apiKey').focus();
        }
      }

      // Escape: Close all open sections
      else if (event.key === 'Escape') {
        if (viEnSection.open || enViSection.open || settingsSection.open) {
          event.preventDefault();
          viEnSection.open = false;
          enViSection.open = false;
          settingsSection.open = false;
        }
      }
    });
  }

  // Initialize keyboard shortcuts
  setupKeyboardShortcuts();

  // --- Translation History ---
  const historyButton = document.getElementById('historyButton');
  const historyPanel = document.getElementById('historyPanel');
  const historyItems = document.getElementById('historyItems');
  const clearHistoryButton = document.getElementById('clearHistory');

  // Preload translation history when popup opens
  let historyLoaded = false;
  let lastHistoryLoadTime = 0;
  loadTranslationHistory();
  historyLoaded = true;

  // Add mouseover event to load history if needed
  historyButton.addEventListener('mouseover', function () {
    // Only reload if it's been a while since the last load
    const now = Date.now();
    if (!historyLoaded || now - lastHistoryLoadTime > 60000) {
      loadTranslationHistory();
    }
  });

  // We don't need to close the panel on click anymore since it's shown/hidden on hover
  // But we'll keep track of clicks on history items

  // Clear history
  clearHistoryButton.addEventListener('click', function () {
    if (confirm('Are you sure you want to clear all translation history?')) {
      chrome.storage.local.set({ 'translationHistory': [] });
      historyItems.innerHTML = '<div class="no-history">No translation history yet</div>';
      showToast('Translation history cleared', 'success');
    }
  });

  // Save translation to history
  function saveToHistory(text, translation, direction) {
    chrome.storage.local.get(['translationHistory'], function (result) {
      let history = result.translationHistory || [];

      // Add new entry at the beginning
      history.unshift({
        text: text,
        translation: translation,
        direction: direction,
        timestamp: Date.now()
      });

      // Limit history to 50 items
      if (history.length > 50) {
        history = history.slice(0, 50);
      }

      chrome.storage.local.set({ 'translationHistory': history });
    });
  }

  // Load translation history
  function loadTranslationHistory() {
    // Update the last load time
    lastHistoryLoadTime = Date.now();

    chrome.storage.local.get(['translationHistory'], function (result) {
      const history = result.translationHistory || [];

      if (history.length === 0) {
        historyItems.innerHTML = '<div class="no-history">No translation history yet</div>';
        return;
      }

      let html = '';

      history.forEach(function (item) {
        const date = new Date(item.timestamp);
        const formattedDate = date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
        const directionText = item.direction === 'en-vi' ? 'English → Vietnamese' : 'Vietnamese → English';

        html += `
          <div class="history-item" data-text="${escapeHTML(item.text)}" data-translation="${escapeHTML(item.translation)}" data-direction="${item.direction}">
            <span class="direction">${directionText}</span>
            <div class="text">${escapeHTML(item.text)}</div>
            <div class="translation">${escapeHTML(item.translation)}</div>
            <div class="timestamp">${formattedDate}</div>
          </div>
        `;
      });

      historyItems.innerHTML = html;

      // Add click event to history items
      document.querySelectorAll('.history-item').forEach(function (item) {
        item.addEventListener('click', function () {
          const text = this.dataset.text;
          const translation = this.dataset.translation;
          const direction = this.dataset.direction;

          // Get all collapsible sections
          const collapsibleSections = document.querySelectorAll('.primary-settings-collapse');
          const viEnSection = collapsibleSections[0];
          const enViSection = collapsibleSections[1];

          // Function to close all sections except the one being opened
          function closeOtherSections(openSection) {
            collapsibleSections.forEach(section => {
              if (section !== openSection) {
                section.open = false;
              }
            });
          }

          // Fill the appropriate input/output fields based on direction
          if (direction === 'en-vi') {
            document.getElementById('translate-input').value = text;
            document.getElementById('translate-output').innerHTML = translation;
            // Open the English to Vietnamese section and close others
            enViSection.open = true;
            closeOtherSections(enViSection);
          } else {
            document.getElementById('vi-input').value = text;
            document.getElementById('en-output').innerHTML = translation;
            // Open the Vietnamese to English section and close others
            viEnSection.open = true;
            closeOtherSections(viEnSection);
          }

          // Show a toast notification
          showToast('Translation loaded', 'success');
        });
      });
    });
  }

  // Helper function to escape HTML
  function escapeHTML(str) {
    return str
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#039;');
  }

  // Create wrapper functions for translation that save to history
  function translateInputWithHistory(text, prompt) {
    return new Promise((resolve) => {
      // Indicate loading state
      translateOutputDiv.innerHTML = 'Translating...';
      translateOutputDiv.setAttribute('aria-busy', 'true');

      chrome.runtime.sendMessage(
        { type: 'translatePopupInput', text: text, prompt: prompt },
        function (response) {
          translateOutputDiv.removeAttribute('aria-busy');

          let translation = '';

          if (chrome.runtime.lastError) {
            console.error('Translation error:', chrome.runtime.lastError.message);
            translateOutputDiv.innerHTML = `<span class="error">Error: ${chrome.runtime.lastError.message}</span>`;
          } else if (response && response.error) {
            console.error('API Error:', response.error);
            translateOutputDiv.innerHTML = `<span class="error">Error: ${response.error}</span>`;
          } else if (response && response.translation) {
            translation = response.translation.trim();

            // Display successful translation using marked.js
            if (typeof marked !== 'undefined' && typeof marked.parse === 'function') {
              try {
                const parsedHTML = marked.parse(translation);
                translateOutputDiv.innerHTML = parsedHTML;
              } catch (parseError) {
                console.error("Error parsing markdown:", parseError);
                translateOutputDiv.textContent = translation;
              }
            } else {
              translateOutputDiv.textContent = translation;
            }

            // Save to history
            if (translation) {
              saveToHistory(text, translation, 'en-vi');
            }
          } else {
            console.error('Unexpected response:', response);
            translateOutputDiv.innerHTML = '<span class="error">Error: Unexpected response from background script.</span>';
          }

          resolve(translation);
        }
      );
    });
  }

  function translateViEnWithHistory(text, prompt) {
    return new Promise((resolve) => {
      // Indicate loading state
      enOutputDiv.innerHTML = 'Translating...';
      enOutputDiv.setAttribute('aria-busy', 'true');

      chrome.runtime.sendMessage(
        { type: 'translateViEnPopupInput', text: text, prompt: prompt },
        function (response) {
          enOutputDiv.removeAttribute('aria-busy');

          let translation = '';

          if (chrome.runtime.lastError) {
            console.error('Translation error:', chrome.runtime.lastError.message);
            enOutputDiv.innerHTML = `<span class="error">Error: ${chrome.runtime.lastError.message}</span>`;
          } else if (response && response.error) {
            console.error('API Error:', response.error);
            enOutputDiv.innerHTML = `<span class="error">Error: ${response.error}</span>`;
          } else if (response && response.translation) {
            translation = response.translation.trim();

            // Display successful translation using marked.js
            if (typeof marked !== 'undefined' && typeof marked.parse === 'function') {
              try {
                const parsedHTML = marked.parse(translation);
                enOutputDiv.innerHTML = parsedHTML;
              } catch (parseError) {
                console.error("Error parsing markdown:", parseError);
                enOutputDiv.textContent = translation;
              }
            } else {
              enOutputDiv.textContent = translation;
            }

            // Save to history
            if (translation) {
              saveToHistory(text, translation, 'vi-en');
            }
          } else {
            console.error('Unexpected response:', response);
            enOutputDiv.innerHTML = '<span class="error">Error: Unexpected response from background script.</span>';
          }

          resolve(translation);
        }
      );
    });
  }

  // --- End Event Listeners ---

});
